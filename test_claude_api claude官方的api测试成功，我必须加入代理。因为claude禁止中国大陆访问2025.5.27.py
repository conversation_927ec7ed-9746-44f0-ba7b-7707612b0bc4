import os
import anthropic
import httpx

def test_claude_api_with_proxy():
    """使用代理测试 Claude API 密钥 (使用 httpx 代理)"""
    # 设置代理
    proxy = "http://127.0.0.1:7890"
    
    # 设置环境变量
    os.environ["HTTP_PROXY"] = proxy
    os.environ["HTTPS_PROXY"] = proxy
    
    # 使用提供的 API 密钥
    api_key = "************************************************************************************************************"
    
    try:
        # 创建 httpx 客户端
        transport = httpx.HTTPTransport(proxy=httpx.Proxy(url=proxy))
        http_client = httpx.Client(transport=transport)
        
        # 初始化 Anthropic 客户端
        client = anthropic.Anthropic(
            api_key=api_key,
            http_client=http_client
        )
        
        # 使用官方示例代码
        message = client.messages.create(
            model="claude-3-5-haiku-20241022",
            max_tokens=1000,
            temperature=1,
            system="你是一个世界级诗人。只用简短的诗歌回应。",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "为什么海水是咸的？"
                        }
                    ]
                }
            ]
        )
        
        # 打印响应
        print("API 密钥有效！")
        print("模型响应:", message.content)
        return True
        
    except Exception as e:
        print("API 密钥测试失败:")
        print(str(e))
        return False

if __name__ == "__main__":
    print("开始使用代理测试 Claude API 密钥...")
    test_claude_api_with_proxy()