<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            font-size: 2.5em;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            color: white;
            font-size: 1.2em;
            font-weight: bold;
        }

        #gameCanvas {
            border: 3px solid white;
            border-radius: 10px;
            background-color: #2c3e50;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .controls {
            margin-top: 20px;
            color: white;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .instructions {
            margin-top: 15px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
            z-index: 1000;
        }

        .game-over h2 {
            margin-top: 0;
            color: #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇游戏</h1>
        
        <div class="game-info">
            <div>分数: <span id="score">0</span></div>
            <div>最高分: <span id="highScore">0</span></div>
        </div>

        <canvas id="gameCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button class="btn" onclick="startGame()">开始游戏</button>
            <button class="btn" onclick="pauseGame()">暂停/继续</button>
            <button class="btn" onclick="resetGame()">重新开始</button>
        </div>

        <div class="instructions">
            <p>使用方向键控制蛇的移动</p>
            <p>吃到红色食物可以增长身体和得分</p>
            <p>避免撞到墙壁或自己的身体</p>
        </div>
    </div>

    <div class="game-over" id="gameOverModal">
        <h2>游戏结束!</h2>
        <p>最终分数: <span id="finalScore">0</span></p>
        <button class="btn" onclick="resetGame()">再来一局</button>
    </div>

    <script src="snake_game.js"></script>
</body>
</html>
