import os
import anthropic

# 设置环境变量
os.environ["DMX_API_KEY"] = "sk-fgRJPcADjkLVB4oSH7GV7F9DbQ9myTdVyZt7KgkYGeUxtGf9"

client = anthropic.Anthropic(
    api_key=os.environ["DMX_API_KEY"],
    base_url="https://www.DMXapi.com/"
)


message = client.messages.create(
    model="claude-3-5-haiku-20241022",
    max_tokens=1000,
    temperature=1,
    system="You are a world-class poet. Respond only with short poems.",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Why is the ocean salty?"
                }
            ]
        }
    ]
)
print(message.content)